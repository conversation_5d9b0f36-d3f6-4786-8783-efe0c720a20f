#!/bin/bash
# Continuous SkyWars System Monitor

echo "🔍 Starting SkyWars Continuous Monitor..."

while true; do
    # Check system health every minute
    sleep 60
    
    # Check server status
    if ! docker ps | grep -q minecraft-server-docker-mc-1; then
        echo "$(date): ❌ Server down, attempting recovery..."
        ./scripts/recovery/server-recovery.sh
    fi
    
    # Check database status
    if ! docker ps | grep -q minecraft-server-docker-db-1; then
        echo "$(date): ❌ Database down, attempting recovery..."
        ./scripts/recovery/database-recovery.sh
    fi
    
    # Check TPS
    TPS=$(docker exec minecraft-server-docker-mc-1 rcon-cli "tps" 2>/dev/null | grep -o '[0-9]*\.[0-9]*' | head -1 || echo "0")
    if (( $(echo "$TPS < 18.0" | bc -l) )); then
        echo "$(date): ⚠️  Low TPS detected: $TPS"
    fi
    
    # Check memory usage
    MEMORY_PERCENT=$(docker stats minecraft-server-docker-mc-1 --no-stream --format "{{.MemPerc}}" 2>/dev/null | sed 's/%//' || echo "0")
    if (( $(echo "$MEMORY_PERCENT > 90" | bc -l) )); then
        echo "$(date): ⚠️  High memory usage: ${MEMORY_PERCENT}%"
        # Restart server if memory usage is too high
        docker-compose restart mc
    fi
done
