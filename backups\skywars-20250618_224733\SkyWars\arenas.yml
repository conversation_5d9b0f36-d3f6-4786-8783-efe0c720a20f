# 🏆 Professional SkyWars Arena Configuration
# Multi-arena system with advanced match management features
# Version: 2.0 - Professional Edition

# 🏟️ Arena Definitions
arenas:
  # Main Solo Arena
  modern_solo:
    # Basic settings
    world: modern_skywar
    enabled: true
    display-name: "&6&lModern SkyWars &7(Solo)"
    description: "&7A modern floating island arena perfect for solo battles!"

    # Game mode configuration
    game-mode: solo
    min-players: 2
    max-players: 12
    optimal-players: 8

    # Timing settings
    time-limit: 900  # 15 minutes
    grace-period: 30  # 30 seconds before PvP starts
    deathmatch-time: 600  # Force deathmatch after 10 minutes

    # Border configuration
    border:
      enabled: true
      initial-size: 120
      final-size: 30
      shrink-start: 300  # Start shrinking after 5 minutes
      shrink-speed: 1
      damage: 2
      warning-time: 30

    # Arena status
    status: active
    priority: 1

    # Spawn points (8 themed islands)
    spawn-points:
      1:  # Forest Island
        x: 1.5
        y: 62.0
        z: -25.5
        yaw: 180.0
        pitch: 0.0
        theme: "forest"
      2:  # Desert Island
        x: 17.5
        y: 62.0
        z: -17.5
        yaw: 225.0
        pitch: 0.0
        theme: "desert"
      3:  # Mountain Island
        x: 23.5
        y: 62.0
        z: 0.5
        yaw: 270.0
        pitch: 0.0
        theme: "mountain"
      4:  # Nether Island
        x: 17.5
        y: 62.0
        z: 17.5
        yaw: 315.0
        pitch: 0.0
        theme: "nether"
      5:  # Ocean Island
        x: 1.5
        y: 62.0
        z: 25.5
        yaw: 0.0
        pitch: 0.0
        theme: "ocean"
      6:  # Ice Island
        x: -17.5
        y: 62.0
        z: 17.5
        yaw: 45.0
        pitch: 0.0
        theme: "ice"
      7:  # End Island
        x: -23.5
        y: 62.0
        z: 0.5
        yaw: 90.0
        pitch: 0.0
        theme: "end"
      8:  # Jungle Island
        x: -17.5
        y: 62.0
        z: -17.5
        yaw: 135.0
        pitch: 0.0
        theme: "jungle"

    # Enhanced chest configuration
    chests:
      center:  # Premium center chests
        - x: 0, y: 64, z: 0, type: "legendary"
        - x: 1, y: 64, z: 0, type: "premium"
        - x: -1, y: 64, z: 0, type: "premium"
        - x: 0, y: 64, z: 1, type: "premium"
        - x: 0, y: 64, z: -1, type: "premium"
      island:  # Island spawn chests
        - x: 1, y: 62, z: -25, type: "basic", theme: "forest"
        - x: 17, y: 62, z: -17, type: "basic", theme: "desert"
        - x: 23, y: 62, z: 0, type: "basic", theme: "mountain"
        - x: 17, y: 62, z: 17, type: "basic", theme: "nether"
        - x: 1, y: 62, z: 25, type: "basic", theme: "ocean"
        - x: -17, y: 62, z: 17, type: "basic", theme: "ice"
        - x: -23, y: 62, z: 0, type: "basic", theme: "end"
        - x: -17, y: 62, z: -17, type: "basic", theme: "jungle"
      floating:  # Premium floating island chests
        - x: 11, y: 76, z: 11, type: "premium"
        - x: -11, y: 76, z: 11, type: "premium"
        - x: 11, y: 76, z: -11, type: "premium"
        - x: -11, y: 76, z: -11, type: "premium"

    # Arena boundaries
    boundaries:
      min-x: -60
      max-x: 60
      min-z: -60
      max-z: 60
      min-y: 45
      max-y: 100

    # Special features
    features:
      death-match: true
      death-match-time: 600  # Force deathmatch after 10 minutes
      spectator-mode: true
      auto-respawn: false
      keep-inventory: false
      natural-regeneration: false
      void-death: true
      fall-damage: true

    # Victory conditions
    victory:
      last-player-standing: true
      time-limit-winner: "most-kills"
      sudden-death: true

    # Arena effects
    effects:
      night-vision: false
      speed: false
      jump-boost: false
      weather: clear
      time: day

  # Doubles Arena (Copy of solo with team settings)
  modern_doubles:
    world: modern_skywar
    enabled: true
    display-name: "&b&lModern SkyWars &7(Doubles)"
    description: "&7Team up with a friend in this epic doubles arena!"

    game-mode: doubles
    min-players: 4
    max-players: 16
    optimal-players: 12

    time-limit: 1200  # 20 minutes for team battles
    grace-period: 45
    deathmatch-time: 900

    border:
      enabled: true
      initial-size: 140
      final-size: 40
      shrink-start: 400
      shrink-speed: 1
      damage: 2
      warning-time: 30

    status: active
    priority: 2

    # Team spawn points (8 teams, 2 players each)
    team-spawns:
      team1:
        player1: {x: 0.5, y: 62.0, z: -25.5, yaw: 180.0, pitch: 0.0}
        player2: {x: 2.5, y: 62.0, z: -25.5, yaw: 180.0, pitch: 0.0}
        theme: "forest"
      team2:
        player1: {x: 16.5, y: 62.0, z: -17.5, yaw: 225.0, pitch: 0.0}
        player2: {x: 18.5, y: 62.0, z: -17.5, yaw: 225.0, pitch: 0.0}
        theme: "desert"
      team3:
        player1: {x: 22.5, y: 62.0, z: 0.5, yaw: 270.0, pitch: 0.0}
        player2: {x: 24.5, y: 62.0, z: 0.5, yaw: 270.0, pitch: 0.0}
        theme: "mountain"
      team4:
        player1: {x: 16.5, y: 62.0, z: 17.5, yaw: 315.0, pitch: 0.0}
        player2: {x: 18.5, y: 62.0, z: 17.5, yaw: 315.0, pitch: 0.0}
        theme: "nether"
      team5:
        player1: {x: 0.5, y: 62.0, z: 25.5, yaw: 0.0, pitch: 0.0}
        player2: {x: 2.5, y: 62.0, z: 25.5, yaw: 0.0, pitch: 0.0}
        theme: "ocean"
      team6:
        player1: {x: -18.5, y: 62.0, z: 17.5, yaw: 45.0, pitch: 0.0}
        player2: {x: -16.5, y: 62.0, z: 17.5, yaw: 45.0, pitch: 0.0}
        theme: "ice"
      team7:
        player1: {x: -24.5, y: 62.0, z: 0.5, yaw: 90.0, pitch: 0.0}
        player2: {x: -22.5, y: 62.0, z: 0.5, yaw: 90.0, pitch: 0.0}
        theme: "end"
      team8:
        player1: {x: -18.5, y: 62.0, z: -17.5, yaw: 135.0, pitch: 0.0}
        player2: {x: -16.5, y: 62.0, z: -17.5, yaw: 135.0, pitch: 0.0}
        theme: "jungle"

    # Same chest and boundary configuration as solo
    chests:
      center:
        - x: 0, y: 64, z: 0, type: "legendary"
        - x: 1, y: 64, z: 0, type: "premium"
        - x: -1, y: 64, z: 0, type: "premium"
        - x: 0, y: 64, z: 1, type: "premium"
        - x: 0, y: 64, z: -1, type: "premium"
      island:
        - x: 1, y: 62, z: -25, type: "basic", theme: "forest"
        - x: 17, y: 62, z: -17, type: "basic", theme: "desert"
        - x: 23, y: 62, z: 0, type: "basic", theme: "mountain"
        - x: 17, y: 62, z: 17, type: "basic", theme: "nether"
        - x: 1, y: 62, z: 25, type: "basic", theme: "ocean"
        - x: -17, y: 62, z: 17, type: "basic", theme: "ice"
        - x: -23, y: 62, z: 0, type: "basic", theme: "end"
        - x: -17, y: 62, z: -17, type: "basic", theme: "jungle"
      floating:
        - x: 11, y: 76, z: 11, type: "premium"
        - x: -11, y: 76, z: 11, type: "premium"
        - x: 11, y: 76, z: -11, type: "premium"
        - x: -11, y: 76, z: -11, type: "premium"

    boundaries:
      min-x: -70
      max-x: 70
      min-z: -70
      max-z: 70
      min-y: 45
      max-y: 100

    features:
      death-match: true
      death-match-time: 900
      spectator-mode: true
      auto-respawn: false
      keep-inventory: false
      natural-regeneration: false
      void-death: true
      fall-damage: true
      team-damage: false  # Prevent friendly fire

    victory:
      last-team-standing: true
      time-limit-winner: "most-kills"
      sudden-death: true

    effects:
      night-vision: false
      speed: false
      jump-boost: false
      weather: clear
      time: day

# 🎯 Arena Management Settings
management:
  # Arena rotation
  rotation:
    enabled: true
    interval: 3600  # 1 hour
    random: true

  # Load balancing
  load-balancing:
    enabled: true
    max-games-per-arena: 1
    distribute-evenly: true

  # Maintenance
  maintenance:
    auto-restart: true
    restart-interval: 21600  # 6 hours
    cleanup-on-restart: true

    # Professional spawn points with themed islands
    spawn-points:
      1:  # Forest Island (North)
        x: 0.5
        y: 62.0
        z: -25.5
        yaw: 180.0
        pitch: 0.0
        theme: "forest"
      2:  # Desert Island (Northeast)
        x: 18.5
        y: 62.0
        z: -17.5
        yaw: 225.0
        pitch: 0.0
        theme: "desert"
      3:  # Mountain Island (East)
        x: 24.5
        y: 62.0
        z: 0.5
        yaw: 270.0
        pitch: 0.0
        theme: "mountain"
      4:  # Nether Island (Southeast)
        x: 18.5
        y: 62.0
        z: 17.5
        yaw: 315.0
        pitch: 0.0
        theme: "nether"
      5:  # Ocean Island (South)
        x: 0.5
        y: 62.0
        z: 25.5
        yaw: 0.0
        pitch: 0.0
        theme: "ocean"
      6:  # Ice Island (Southwest)
        x: -18.5
        y: 62.0
        z: 17.5
        yaw: 45.0
        pitch: 0.0
        theme: "ice"
      7:  # End Island (West)
        x: -24.5
        y: 62.0
        z: 0.5
        yaw: 90.0
        pitch: 0.0
        theme: "end"
      8:  # Jungle Island (Northwest)
        x: -18.5
        y: 62.0
        z: -17.5
        yaw: 135.0
        pitch: 0.0
        theme: "jungle"

    # Enhanced chest configuration
    chests:
      center:  # Premium center chests
        - x: 0, y: 64, z: 0
        - x: 1, y: 64, z: 0
        - x: -1, y: 64, z: 0
        - x: 0, y: 64, z: 1
      island:  # Island spawn chests
        - x: 1, y: 62, z: -25    # Forest
        - x: 17, y: 62, z: -17   # Desert
        - x: 23, y: 62, z: 0     # Mountain
        - x: 17, y: 62, z: 17    # Nether
        - x: 1, y: 62, z: 25     # Ocean
        - x: -17, y: 62, z: 17   # Ice
        - x: -23, y: 62, z: 0    # End
        - x: -17, y: 62, z: -17  # Jungle
      floating:  # Premium floating island chests
        - x: 11, y: 76, z: 11    # Gold island
        - x: -11, y: 76, z: 11   # Diamond island
        - x: 11, y: 76, z: -11   # Emerald island
        - x: -11, y: 76, z: -11  # Iron island

    # Arena boundaries
    boundaries:
      min-x: -60
      max-x: 60
      min-z: -60
      max-z: 60
      min-y: 45
      max-y: 100

    # Special features
    features:
      death-match: true
      death-match-time: 600  # Force deathmatch after 10 minutes
      spectator-mode: true
      auto-respawn: false
      keep-inventory: false
      natural-regeneration: false

    # Victory conditions
    victory:
      last-player-standing: true
      time-limit-winner: "most-kills"

    # Arena effects
    effects:
      night-vision: false
      speed: false
      jump-boost: false
