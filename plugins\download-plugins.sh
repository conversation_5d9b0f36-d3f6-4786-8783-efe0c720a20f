#!/bin/bash
# Download Essential Minecraft Server Plugins
# This script downloads the required plugin J<PERSON> files

echo "🔽 Downloading essential Minecraft server plugins..."

# Create plugins directory if it doesn't exist
mkdir -p plugins

# Download AuthMe Reloaded (Authentication plugin)
echo "📥 Downloading AuthMe Reloaded..."
curl -L -o plugins/AuthMe-5.6.0.jar "https://github.com/AuthMe/AuthMeReloaded/releases/download/5.6.0/AuthMe-5.6.0.jar"

# Download EssentialsX (Core server utilities)
echo "📥 Downloading EssentialsX..."
curl -L -o plugins/EssentialsX-2.21.0.jar "https://github.com/EssentialsX/Essentials/releases/download/2.21.0/EssentialsX-2.21.0.jar"

# Download Multiverse-Core (World management)
echo "📥 Downloading Multiverse-Core..."
curl -L -o plugins/Multiverse-Core-4.3.12.jar "https://github.com/Multiverse/Multiverse-Core/releases/download/4.3.12/multiverse-core-4.3.12.jar"

# Download LuckPerms (Permissions management)
echo "📥 Downloading LuckPerms..."
curl -L -o plugins/LuckPerms-Bukkit-5.4.137.jar "https://download.luckperms.net/1556/bukkit/LuckPerms-Bukkit-5.4.137.jar"

# Download Vault (Economy API - required by many plugins)
echo "📥 Downloading Vault..."
curl -L -o plugins/Vault-1.7.3.jar "https://github.com/MilkBowl/Vault/releases/download/1.7.3/Vault.jar"

echo "✅ Plugin download completed!"
echo "🔄 Please restart your Minecraft server to load the new plugins."
