#!/bin/bash
# Multiverse Integration for SkyWars

# Function to create SkyWars world if it doesn't exist
create_skywars_world() {
    local world_name="$1"
    
    # Check if world exists in Multiverse
    local world_exists=$(docker exec minecraft-server-docker-mc-1 rcon-cli "mv list" 2>/dev/null | grep "$world_name" || echo "")
    
    if [[ -z "$world_exists" ]]; then
        log_info "Creating world $world_name in Multiverse..."
        docker exec minecraft-server-docker-mc-1 rcon-cli "mv create $world_name normal" 2>/dev/null
        docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set gamemode creative $world_name" 2>/dev/null
        docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set pvp true $world_name" 2>/dev/null
        log_success "World $world_name created"
    else
        log_success "World $world_name already exists"
    fi
}

# Function to teleport player to world
teleport_to_world() {
    local player="$1"
    local world="$2"
    local x="${3:-0}"
    local y="${4:-70}"
    local z="${5:-0}"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "mv tp $player $world" 2>/dev/null
}

# Function to set world properties for SkyWars
configure_skywars_world() {
    local world_name="$1"
    
    # Set world properties
    docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set gamemode survival $world_name" 2>/dev/null
    docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set pvp true $world_name" 2>/dev/null
    docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set difficulty normal $world_name" 2>/dev/null
    docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set allowFlight false $world_name" 2>/dev/null
    
    log_success "World $world_name configured for SkyWars"
}

# Export functions
export -f create_skywars_world
export -f teleport_to_world
export -f configure_skywars_world
