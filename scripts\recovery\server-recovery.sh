#!/bin/bash
# Server Recovery Script

echo "🔧 Server Recovery Procedure"
echo "============================"

# Check server status
if ! docker ps | grep -q minecraft-server-docker-mc-1; then
    echo "❌ Server is not running"
    
    # Start server
    echo "🔄 Starting server..."
    docker-compose up -d mc
    
    # Wait for server to start
    for i in {1..60}; do
        if docker-compose logs mc | tail -10 | grep -q "Done\|Timings Reset"; then
            echo "✅ Server recovered"
            exit 0
        fi
        sleep 3
    done
    
    echo "❌ Server recovery failed"
    exit 1
else
    echo "✅ Server is healthy"
fi
