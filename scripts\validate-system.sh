#!/bin/bash
# System Validation Script

echo "🔍 SkyWars System Validation"
echo "============================"

ERRORS=0

# Validate Docker containers
echo "Checking Docker containers..."
if ! docker ps | grep -q minecraft-server-docker-mc-1; then
    echo "❌ Minecraft server container not running"
    ((ERRORS++))
else
    echo "✅ Minecraft server container running"
fi

if ! docker ps | grep -q minecraft-server-docker-db-1; then
    echo "❌ Database container not running"
    ((ERRORS++))
else
    echo "✅ Database container running"
fi

# Validate configuration files
echo "Checking configuration files..."
if [[ -f "plugins/SkyWars/config.yml" ]]; then
    echo "✅ Main configuration exists"
else
    echo "❌ Main configuration missing"
    ((ERRORS++))
fi

# Validate world
echo "Checking world files..."
if [[ -d "modern_skywar" ]]; then
    echo "✅ SkyWars world exists"
else
    echo "❌ SkyWars world missing"
    ((ERRORS++))
fi

# Validate database connection
echo "Checking database connection..."
if docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    ((ERRORS++))
fi

# Summary
echo ""
echo "Validation Summary:"
echo "=================="
if [[ $ERRORS -eq 0 ]]; then
    echo "✅ All checks passed! System is healthy."
    exit 0
else
    echo "❌ $ERRORS error(s) found. System needs attention."
    exit 1
fi
