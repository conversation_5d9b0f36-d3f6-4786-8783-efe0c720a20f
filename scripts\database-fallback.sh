#!/bin/bash
# Database Fallback System

# Function to check database health
check_database_health() {
    if docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to use file-based storage as fallback
use_file_storage() {
    local player="$1"
    local action="$2"
    local data="$3"
    
    local storage_dir="data/fallback-storage"
    mkdir -p "$storage_dir"
    
    case "$action" in
        "save_stats")
            echo "$data" > "$storage_dir/${player}_stats.txt"
            ;;
        "load_stats")
            if [[ -f "$storage_dir/${player}_stats.txt" ]]; then
                cat "$storage_dir/${player}_stats.txt"
            else
                echo "wins:0,losses:0,kills:0,deaths:0"
            fi
            ;;
        "save_game")
            echo "$(date):$data" >> "$storage_dir/${player}_games.log"
            ;;
    esac
}

# Function to attempt database recovery
attempt_database_recovery() {
    echo "Attempting database recovery..."
    
    # Restart database container
    docker-compose restart db
    
    # Wait for recovery
    for i in {1..30}; do
        if check_database_health; then
            echo "Database recovered successfully"
            return 0
        fi
        sleep 2
    done
    
    echo "Database recovery failed, using file storage"
    return 1
}

# Main fallback handler
handle_database_fallback() {
    if ! check_database_health; then
        echo "Database unavailable, attempting recovery..."
        
        if ! attempt_database_recovery; then
            echo "Using file-based storage as fallback"
            export USE_FILE_STORAGE=true
        fi
    fi
}

# Export functions
export -f check_database_health
export -f use_file_storage
export -f attempt_database_recovery
export -f handle_database_fallback
