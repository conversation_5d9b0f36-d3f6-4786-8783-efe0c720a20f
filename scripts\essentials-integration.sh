#!/bin/bash
# Essentials Integration for SkyWars

# Function to check player balance
check_player_balance() {
    local player="$1"
    
    local balance=$(docker exec minecraft-server-docker-mc-1 rcon-cli "money $player" 2>/dev/null | grep -o '[0-9]*\.[0-9]*' || echo "0")
    echo "$balance"
}

# Function to give money to player
give_money() {
    local player="$1"
    local amount="$2"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "eco give $player $amount" 2>/dev/null
}

# Function to take money from player
take_money() {
    local player="$1"
    local amount="$2"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "eco take $player $amount" 2>/dev/null
}

# Function to teleport player to lobby
teleport_to_lobby() {
    local player="$1"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "spawn $player" 2>/dev/null
}

# Export functions
export -f check_player_balance
export -f give_money
export -f take_money
export -f teleport_to_lobby
