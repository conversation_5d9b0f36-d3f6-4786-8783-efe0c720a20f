#!/bin/bash
# Database Recovery Script

echo "🔧 Database Recovery Procedure"
echo "=============================="

# Check database connection
if ! docker exec mincraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
    echo "❌ Database connection failed"
    
    # Restart database
    echo "🔄 Restarting database..."
    docker-compose restart db
    
    # Wait for database to start
    for i in {1..30}; do
        if docker exec mincraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
            echo "✅ Database recovered"
            exit 0
        fi
        sleep 2
    done
    
    echo "❌ Database recovery failed"
    exit 1
else
    echo "✅ Database is healthy"
fi
