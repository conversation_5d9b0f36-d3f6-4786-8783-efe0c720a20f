# 🔧 Professional SkyWars Administrative Tools
# Advanced admin commands and management features
# Version: 2.0 - Professional Edition

# 👑 Admin Permissions
permissions:
  # Permission levels
  levels:
    moderator:
      permissions:
        - "skywars.admin.basic"
        - "skywars.admin.kick"
        - "skywars.admin.spectate"
        - "skywars.admin.stats.view"
      commands:
        - "/sw kick"
        - "/sw spectate"
        - "/sw stats"
        - "/sw info"
        
    admin:
      permissions:
        - "skywars.admin.*"
        - "skywars.admin.arena.*"
        - "skywars.admin.game.*"
        - "skywars.admin.player.*"
      commands:
        - "/sw admin"
        - "/sw arena"
        - "/sw game"
        - "/sw reload"
        
    owner:
      permissions:
        - "skywars.*"
        - "skywars.admin.system"
        - "skywars.admin.database"
        - "skywars.admin.maintenance"
      commands:
        - "/sw system"
        - "/sw database"
        - "/sw maintenance"

# 🎮 Game Management Commands
game-management:
  # Force start games
  force-start:
    enabled: true
    permission: "skywars.admin.game.start"
    command: "/sw game start <arena>"
    description: "Force start a game in specified arena"
    
  # Force end games
  force-end:
    enabled: true
    permission: "skywars.admin.game.end"
    command: "/sw game end <arena>"
    description: "Force end a game in specified arena"
    
  # Pause/Resume games
  pause-resume:
    enabled: true
    permission: "skywars.admin.game.pause"
    commands:
      pause: "/sw game pause <arena>"
      resume: "/sw game resume <arena>"
    description: "Pause or resume games"
    
  # Game information
  game-info:
    enabled: true
    permission: "skywars.admin.game.info"
    command: "/sw game info <arena>"
    description: "View detailed game information"

# 🏟️ Arena Management Commands
arena-management:
  # Create arena
  create-arena:
    enabled: true
    permission: "skywars.admin.arena.create"
    command: "/sw arena create <name> <world>"
    description: "Create a new SkyWars arena"
    
  # Delete arena
  delete-arena:
    enabled: true
    permission: "skywars.admin.arena.delete"
    command: "/sw arena delete <name>"
    description: "Delete an existing arena"
    confirmation-required: true
    
  # Enable/Disable arena
  toggle-arena:
    enabled: true
    permission: "skywars.admin.arena.toggle"
    commands:
      enable: "/sw arena enable <name>"
      disable: "/sw arena disable <name>"
    description: "Enable or disable arenas"
    
  # Arena configuration
  configure-arena:
    enabled: true
    permission: "skywars.admin.arena.config"
    commands:
      set-spawn: "/sw arena setspawn <arena> <number>"
      set-chest: "/sw arena setchest <arena> <type>"
      set-boundary: "/sw arena setboundary <arena>"
    description: "Configure arena settings"
    
  # Arena restoration
  restore-arena:
    enabled: true
    permission: "skywars.admin.arena.restore"
    command: "/sw arena restore <name>"
    description: "Manually restore an arena"

# 👥 Player Management Commands
player-management:
  # Kick player from game
  kick-player:
    enabled: true
    permission: "skywars.admin.player.kick"
    command: "/sw kick <player> [reason]"
    description: "Kick a player from their current game"
    
  # Ban player from SkyWars
  ban-player:
    enabled: true
    permission: "skywars.admin.player.ban"
    command: "/sw ban <player> <duration> [reason]"
    description: "Ban a player from SkyWars"
    
  # Unban player
  unban-player:
    enabled: true
    permission: "skywars.admin.player.unban"
    command: "/sw unban <player>"
    description: "Unban a player from SkyWars"
    
  # Force spectate
  force-spectate:
    enabled: true
    permission: "skywars.admin.player.spectate"
    command: "/sw spectate <player> <arena>"
    description: "Force a player to spectate a game"
    
  # Reset player stats
  reset-stats:
    enabled: true
    permission: "skywars.admin.player.reset"
    command: "/sw reset <player> [stat]"
    description: "Reset player statistics"
    confirmation-required: true

# 📊 Statistics & Monitoring
statistics-monitoring:
  # View detailed stats
  detailed-stats:
    enabled: true
    permission: "skywars.admin.stats.detailed"
    command: "/sw stats <player> detailed"
    description: "View detailed player statistics"
    
  # Server statistics
  server-stats:
    enabled: true
    permission: "skywars.admin.stats.server"
    command: "/sw serverstats"
    description: "View server-wide SkyWars statistics"
    
  # Performance monitoring
  performance:
    enabled: true
    permission: "skywars.admin.monitor"
    command: "/sw monitor"
    description: "View performance metrics"
    
  # Database statistics
  database-stats:
    enabled: true
    permission: "skywars.admin.database.stats"
    command: "/sw dbstats"
    description: "View database statistics"

# 🔧 System Management
system-management:
  # Reload configuration
  reload:
    enabled: true
    permission: "skywars.admin.reload"
    command: "/sw reload [config]"
    description: "Reload SkyWars configuration"
    
  # Maintenance mode
  maintenance:
    enabled: true
    permission: "skywars.admin.maintenance"
    commands:
      enable: "/sw maintenance on [reason]"
      disable: "/sw maintenance off"
    description: "Enable/disable maintenance mode"
    
  # Backup system
  backup:
    enabled: true
    permission: "skywars.admin.backup"
    commands:
      create: "/sw backup create"
      restore: "/sw backup restore <name>"
      list: "/sw backup list"
    description: "Manage SkyWars backups"
    
  # Update system
  update:
    enabled: true
    permission: "skywars.admin.update"
    command: "/sw update [version]"
    description: "Update SkyWars system"

# 🎯 Queue Management
queue-management:
  # View queue status
  queue-status:
    enabled: true
    permission: "skywars.admin.queue.view"
    command: "/sw queue status"
    description: "View current queue status"
    
  # Clear queues
  clear-queue:
    enabled: true
    permission: "skywars.admin.queue.clear"
    command: "/sw queue clear [type]"
    description: "Clear player queues"
    
  # Force queue player
  force-queue:
    enabled: true
    permission: "skywars.admin.queue.force"
    command: "/sw queue add <player> <type>"
    description: "Force add player to queue"
    
  # Remove from queue
  remove-queue:
    enabled: true
    permission: "skywars.admin.queue.remove"
    command: "/sw queue remove <player>"
    description: "Remove player from queue"

# 🔍 Debug & Testing
debug-testing:
  # Debug mode
  debug:
    enabled: true
    permission: "skywars.admin.debug"
    command: "/sw debug [on|off]"
    description: "Toggle debug mode"
    
  # Test commands
  test:
    enabled: true
    permission: "skywars.admin.test"
    commands:
      arena: "/sw test arena <name>"
      queue: "/sw test queue <type>"
      stats: "/sw test stats <player>"
    description: "Test various system components"
    
  # Simulate events
  simulate:
    enabled: true
    permission: "skywars.admin.simulate"
    commands:
      kill: "/sw simulate kill <player> <killer>"
      win: "/sw simulate win <player>"
      game: "/sw simulate game <arena>"
    description: "Simulate game events for testing"

# 📋 Command Aliases
aliases:
  # Short aliases for common commands
  short:
    "/swa": "/sw admin"
    "/swg": "/sw game"
    "/swar": "/sw arena"
    "/swp": "/sw player"
    "/sws": "/sw stats"
    
  # Admin shortcuts
  admin:
    "/swstart": "/sw game start"
    "/swend": "/sw game end"
    "/swkick": "/sw kick"
    "/swban": "/sw ban"
    "/swreload": "/sw reload"

# 🎨 Admin Interface
interface:
  # Admin GUI
  gui:
    enabled: true
    title: "&c&lSkyWars Admin Panel"
    size: 54  # 6 rows
    
    # GUI items
    items:
      game-management:
        slot: 10
        item: "DIAMOND_SWORD"
        name: "&a&lGame Management"
        lore:
          - "&7Manage active games"
          - "&7Start, stop, pause games"
          - ""
          - "&eClick to open!"
          
      arena-management:
        slot: 12
        item: "GRASS_BLOCK"
        name: "&b&lArena Management"
        lore:
          - "&7Manage SkyWars arenas"
          - "&7Create, delete, configure"
          - ""
          - "&eClick to open!"
          
      player-management:
        slot: 14
        item: "PLAYER_HEAD"
        name: "&c&lPlayer Management"
        lore:
          - "&7Manage players"
          - "&7Kick, ban, spectate"
          - ""
          - "&eClick to open!"
          
      statistics:
        slot: 16
        item: "BOOK"
        name: "&e&lStatistics"
        lore:
          - "&7View statistics"
          - "&7Player and server stats"
          - ""
          - "&eClick to open!"
          
      system:
        slot: 28
        item: "REDSTONE"
        name: "&d&lSystem Management"
        lore:
          - "&7System controls"
          - "&7Reload, maintenance, backup"
          - ""
          - "&eClick to open!"
          
      queue:
        slot: 30
        item: "COMPASS"
        name: "&6&lQueue Management"
        lore:
          - "&7Manage player queues"
          - "&7View status, clear queues"
          - ""
          - "&eClick to open!"
          
      debug:
        slot: 32
        item: "COMMAND_BLOCK"
        name: "&8&lDebug & Testing"
        lore:
          - "&7Debug tools"
          - "&7Test features, simulate events"
          - ""
          - "&eClick to open!"
          
  # Command suggestions
  suggestions:
    enabled: true
    show-on-tab: true
    context-aware: true

# 📊 Logging & Audit
logging:
  # Admin action logging
  admin-actions:
    enabled: true
    log-file: "skywars-admin.log"
    log-level: "INFO"
    
    # Logged actions
    logged-actions:
      - "game-start"
      - "game-end"
      - "player-kick"
      - "player-ban"
      - "arena-create"
      - "arena-delete"
      - "config-reload"
      - "maintenance-toggle"
      
  # Audit trail
  audit:
    enabled: true
    database-logging: true
    retention-days: 90
    
# 🚨 Alerts & Notifications
alerts:
  # System alerts
  system:
    enabled: true
    
    # Alert conditions
    conditions:
      high-memory-usage: 80  # percentage
      low-tps: 18.0
      database-errors: 5     # per minute
      queue-overflow: 100    # players
      
  # Admin notifications
  notifications:
    discord: false
    email: false
    in-game: true
    
# 🔐 Security
security:
  # Command verification
  verification:
    enabled: true
    require-confirmation: ["delete", "reset", "ban"]
    timeout: 30  # seconds
    
  # Rate limiting
  rate-limiting:
    enabled: true
    max-commands-per-minute: 60
    
  # IP restrictions
  ip-restrictions:
    enabled: false
    allowed-ips: []
