# 👻 Professional SkyWars Spectator System
# Advanced spectator features for enhanced viewing experience
# Version: 2.0 - Professional Edition

# 👁️ Spectator Configuration
spectator:
  # Global settings
  global:
    enabled: true
    max-spectators-per-game: 50
    allow-spectate-from-lobby: true
    auto-spectate-on-death: true
    spectator-chat: true
    
  # Spectator permissions
  permissions:
    # Basic spectator abilities
    basic:
      fly: true
      no-clip: true
      invisible-to-players: true
      see-other-spectators: true
      
    # Advanced abilities
    advanced:
      teleport-to-players: true
      follow-players: true
      speed-control: true
      night-vision: true
      
    # Restricted abilities
    restrictions:
      interact-with-world: false
      pickup-items: false
      damage-players: false
      use-commands: ["spectator-only"]
      chat-with-players: false

# 🎮 Spectator Features
features:
  # Player following
  player-following:
    enabled: true
    auto-switch-on-death: true
    smooth-camera: true
    follow-distance: 5.0
    
  # Teleportation
  teleportation:
    enabled: true
    teleport-menu: true
    quick-teleport: true
    teleport-cooldown: 1  # seconds
    
  # Speed control
  speed-control:
    enabled: true
    default-speed: 0.2
    min-speed: 0.1
    max-speed: 1.0
    speed-increment: 0.1
    
  # Night vision
  night-vision:
    enabled: true
    auto-apply: true
    toggle-command: true
    
  # Compass menu
  compass-menu:
    enabled: true
    item: "COMPASS"
    name: "&b&lPlayer Tracker"
    lore:
      - "&7Right-click to open"
      - "&7player teleport menu"
    slot: 0

# 🎨 Spectator Interface
interface:
  # Hotbar items
  hotbar:
    # Player tracker (compass)
    player-tracker:
      enabled: true
      slot: 0
      item: "COMPASS"
      name: "&b&lPlayer Tracker"
      lore:
        - "&7Right-click to teleport"
        - "&7to a specific player"
        
    # Speed controller
    speed-controller:
      enabled: true
      slot: 1
      item: "FEATHER"
      name: "&e&lSpeed Control"
      lore:
        - "&7Left-click: Decrease speed"
        - "&7Right-click: Increase speed"
        - "&7Current: &e%speed%x"
        
    # Night vision toggle
    night-vision-toggle:
      enabled: true
      slot: 2
      item: "GOLDEN_CARROT"
      name: "&6&lNight Vision"
      lore:
        - "&7Click to toggle"
        - "&7night vision"
        - "&7Status: %status%"
        
    # Game information
    game-info:
      enabled: true
      slot: 4
      item: "BOOK"
      name: "&a&lGame Information"
      lore:
        - "&7Arena: &e%arena%"
        - "&7Mode: &e%mode%"
        - "&7Players: &e%players%"
        - "&7Time: &e%time%"
        
    # Statistics viewer
    stats-viewer:
      enabled: true
      slot: 7
      item: "PAPER"
      name: "&d&lMatch Statistics"
      lore:
        - "&7Click to view"
        - "&7current match stats"
        
    # Leave spectator
    leave-spectator:
      enabled: true
      slot: 8
      item: "BARRIER"
      name: "&c&lLeave Spectator"
      lore:
        - "&7Click to return"
        - "&7to the lobby"

# 📋 Player Tracker Menu
player-tracker:
  # Menu settings
  menu:
    title: "&b&lPlayer Tracker"
    size: 54  # 6 rows
    update-interval: 2  # seconds
    
  # Player item format
  player-item:
    alive:
      item: "PLAYER_HEAD"
      name: "&a%player%"
      lore:
        - "&7Health: &c%health%❤"
        - "&7Kills: &e%kills%"
        - "&7Status: &aAlive"
        - ""
        - "&eClick to teleport!"
        
    dead:
      item: "SKELETON_SKULL"
      name: "&7%player%"
      lore:
        - "&7Status: &cEliminated"
        - "&7Placement: &e#%placement%"
        - "&7Kills: &e%kills%"
        - ""
        - "&cCannot teleport"
        
  # Menu navigation
  navigation:
    previous-page:
      slot: 45
      item: "ARROW"
      name: "&e&lPrevious Page"
      
    next-page:
      slot: 53
      item: "ARROW"
      name: "&e&lNext Page"
      
    close-menu:
      slot: 49
      item: "BARRIER"
      name: "&c&lClose Menu"

# 📊 Spectator Statistics
statistics:
  # Real-time stats display
  real-time:
    enabled: true
    update-interval: 5  # seconds
    
    # Displayed statistics
    stats:
      - "Players Remaining: &e%remaining%"
      - "Total Kills: &c%total_kills%"
      - "Game Time: &a%game_time%"
      - "Current Phase: &b%phase%"
      - "Spectators: &7%spectators%"
      
  # Match statistics menu
  match-stats:
    enabled: true
    
    # Statistics categories
    categories:
      overview:
        name: "&a&lMatch Overview"
        items:
          - "Arena: &e%arena%"
          - "Game Mode: &e%mode%"
          - "Start Time: &e%start_time%"
          - "Duration: &e%duration%"
          - "Players: &e%total_players%"
          
      leaderboard:
        name: "&6&lCurrent Leaderboard"
        items:
          - "&61st: &e%first_player% &7(&c%first_kills% kills&7)"
          - "&72nd: &e%second_player% &7(&c%second_kills% kills&7)"
          - "&c3rd: &e%third_player% &7(&c%third_kills% kills&7)"
          
      events:
        name: "&c&lRecent Events"
        items:
          - "%recent_events%"

# 💬 Spectator Chat
chat:
  # Chat settings
  settings:
    enabled: true
    separate-channel: true
    see-player-chat: true
    cross-spectator-chat: true
    
  # Chat formatting
  formatting:
    spectator-prefix: "&7[SPEC] "
    spectator-format: "&7[SPEC] %player%: &f%message%"
    player-chat-format: "&8[GAME] %player%: &7%message%"
    
  # Chat commands
  commands:
    toggle-chat: "/spec chat"
    toggle-player-chat: "/spec playerchat"
    
# 🎯 Spectator Events
events:
  # Event notifications
  notifications:
    player-kill: true
    player-death: true
    game-phase-change: true
    border-shrink: true
    chest-refill: true
    
  # Event formatting
  formatting:
    kill: "&c%killer% &7eliminated &c%victim%&7!"
    death: "&c%player% &7died!"
    phase-change: "&e&lPhase changed to: &b%phase%"
    border-shrink: "&c&lBorder is shrinking!"
    chest-refill: "&e&lChests have been refilled!"
    
  # Event sounds
  sounds:
    kill: "ENTITY_LIGHTNING_BOLT_THUNDER"
    death: "ENTITY_GHAST_DEATH"
    phase-change: "BLOCK_NOTE_BLOCK_PLING"
    border-shrink: "ENTITY_ENDER_DRAGON_GROWL"

# 🔧 Advanced Features
advanced:
  # Spectator analytics
  analytics:
    enabled: true
    track-viewing-time: true
    track-popular-players: true
    track-spectator-behavior: true
    
  # Anti-cheat integration
  anti-cheat:
    enabled: true
    prevent-ghosting: true
    hide-spectators-from-players: true
    spectator-mode-detection: true
    
  # Performance optimization
  performance:
    limit-spectator-updates: true
    optimize-rendering: true
    reduce-packet-spam: true
    
# 🎨 Customization
customization:
  # Visual effects
  effects:
    join-spectator: "VILLAGER_HAPPY"
    leave-spectator: "SMOKE_NORMAL"
    teleport: "PORTAL"
    
  # Sounds
  sounds:
    join-spectator: "ENTITY_EXPERIENCE_ORB_PICKUP"
    leave-spectator: "ENTITY_VILLAGER_NO"
    teleport: "ENTITY_ENDERMAN_TELEPORT"
    menu-click: "UI_BUTTON_CLICK"
    
  # Particles
  particles:
    spectator-trail: false
    teleport-effect: true
    
# 🚫 Restrictions
restrictions:
  # Spectator limitations
  limitations:
    max-spectate-time: 1800  # 30 minutes
    cooldown-between-games: 10  # seconds
    
  # Blocked actions
  blocked-actions:
    - "INTERACT_ENTITY"
    - "INTERACT_BLOCK"
    - "PICKUP_ITEM"
    - "DROP_ITEM"
    - "DAMAGE_ENTITY"
    - "BREAK_BLOCK"
    - "PLACE_BLOCK"
    
  # Blocked commands
  blocked-commands:
    - "/tp"
    - "/teleport"
    - "/gamemode"
    - "/give"
    - "/kill"
    
# 📱 Mobile/Bedrock Support
mobile-support:
  # Bedrock compatibility
  bedrock:
    enabled: false
    simplified-ui: true
    touch-controls: true
    
  # Mobile optimizations
  optimizations:
    reduced-particles: true
    simplified-menus: true
    larger-buttons: true

# 🔄 Integration
integration:
  # Plugin integrations
  plugins:
    # Discord integration
    discord:
      enabled: false
      spectator-notifications: true
      
    # Streaming integration
    streaming:
      enabled: false
      obs-integration: true
      
    # Replay system
    replay:
      enabled: false
      auto-record: true
