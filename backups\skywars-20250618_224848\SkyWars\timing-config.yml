# ⏱️ Professional SkyWars Timing System
# Advanced match timing and progression management
# Version: 2.0 - Professional Edition

# 🎮 Match Phases Configuration
phases:
  # Pre-Game Phase
  pre-game:
    # Queue waiting
    queue-wait:
      minimum-time: 15      # Minimum wait for more players
      maximum-time: 120     # Maximum wait before force start
      optimal-players-time: 30  # Wait time when optimal players reached
      
    # Starting countdown
    countdown:
      duration: 10          # Countdown duration in seconds
      cancel-threshold: 1   # Minimum players to not cancel
      announcements: [10, 5, 3, 2, 1]  # Announcement times
      
    # Preparation phase
    preparation:
      duration: 15          # Time to prepare after teleport
      allow-movement: false # Allow player movement
      allow-chat: true      # Allow chat during prep
      show-tips: true       # Show gameplay tips
      
  # Active Game Phases
  active-game:
    # Grace period (no PvP)
    grace-period:
      duration: 30          # Grace period duration
      announcements: [30, 15, 10, 5, 3, 2, 1]
      effects:
        - "DAMAGE_RESISTANCE:255"  # Complete damage immunity
        - "SLOW:1"                 # Slight slowness
      particles: "VILLAGER_HAPPY"
      
    # Normal gameplay phase
    normal-phase:
      duration: 300         # 5 minutes of normal gameplay
      chest-refills: [180, 360]  # Chest refill times
      announcements: [300, 240, 180, 120, 60, 30]
      
    # Border shrinking phase
    border-phase:
      start-time: 300       # Start after 5 minutes
      duration: 300         # 5 minutes to shrink
      initial-size: 120     # Starting border size
      final-size: 30        # Final border size
      damage-per-second: 2  # Damage outside border
      warning-time: 30      # Warning before shrinking
      announcements: [60, 30, 15, 10, 5]
      
    # Deathmatch phase
    deathmatch:
      trigger-time: 600     # Force deathmatch after 10 minutes
      duration: 180         # 3 minutes for deathmatch
      teleport-center: true # Teleport all players to center
      center-radius: 15     # Deathmatch area radius
      announcements: [180, 120, 60, 30, 15, 10, 5]
      effects:
        - "SPEED:1"         # Speed boost
        - "STRENGTH:1"      # Strength boost
      
    # Sudden death phase
    sudden-death:
      trigger-time: 780     # After 13 minutes total
      damage-interval: 5    # Damage every 5 seconds
      damage-amount: 2      # Damage per interval
      damage-increase: 1    # Increase damage over time
      max-damage: 10        # Maximum damage per interval
      announcements: [60, 30, 15, 10, 5]
      
  # Post-Game Phase
  post-game:
    # Victory celebration
    victory:
      duration: 10          # Victory screen duration
      fireworks: true       # Launch fireworks
      title-duration: 5     # Title display duration
      
    # Statistics display
    statistics:
      duration: 15          # Stats display duration
      detailed-stats: true  # Show detailed statistics
      leaderboard: true     # Show match leaderboard
      
    # Cleanup phase
    cleanup:
      duration: 5           # Cleanup duration
      teleport-lobby: true  # Teleport players to lobby
      clear-effects: true   # Clear all potion effects
      restore-inventory: false  # Don't restore inventory

# ⏰ Dynamic Timing System
dynamic-timing:
  enabled: true
  
  # Player count adjustments
  player-adjustments:
    # Fewer players = faster game
    low-player-multiplier: 0.75   # 25% faster with <50% players
    high-player-multiplier: 1.25  # 25% slower with >90% players
    
  # Skill-based adjustments (for ranked)
  skill-adjustments:
    enabled: false
    high-skill-multiplier: 1.1    # 10% longer for high-skill games
    low-skill-multiplier: 0.9     # 10% shorter for low-skill games
    
  # Performance adjustments
  performance-adjustments:
    enabled: true
    low-tps-multiplier: 1.2       # 20% longer if server struggling
    high-load-multiplier: 1.1     # 10% longer under high load

# 🎯 Game Mode Specific Timing
game-modes:
  solo:
    grace-period: 30
    normal-phase: 300
    border-start: 300
    deathmatch: 600
    sudden-death: 780
    max-duration: 900
    
  doubles:
    grace-period: 45
    normal-phase: 360
    border-start: 360
    deathmatch: 720
    sudden-death: 900
    max-duration: 1080
    
  squads:
    grace-period: 60
    normal-phase: 420
    border-start: 420
    deathmatch: 840
    sudden-death: 1020
    max-duration: 1200
    
  ranked:
    grace-period: 30
    normal-phase: 360
    border-start: 360
    deathmatch: 720
    sudden-death: 900
    max-duration: 1080

# 📢 Announcement System
announcements:
  # Global announcements
  global:
    enabled: true
    format: "&8[&b&lSkyWars&8] &e%message%"
    sound: "BLOCK_NOTE_BLOCK_PLING"
    
  # Phase-specific announcements
  phase-specific:
    grace-period:
      format: "&e&l⚠ &ePvP starts in &c&l%time% &eseconds!"
      sound: "ENTITY_EXPERIENCE_ORB_PICKUP"
      
    border-shrink:
      format: "&c&l⚠ &cBorder shrinking in &e&l%time% &cseconds!"
      sound: "ENTITY_ENDER_DRAGON_GROWL"
      
    deathmatch:
      format: "&4&l💀 &4Deathmatch in &c&l%time% &4seconds!"
      sound: "ENTITY_WITHER_SPAWN"
      
    sudden-death:
      format: "&4&l💀 &4SUDDEN DEATH in &c&l%time% &4seconds!"
      sound: "ENTITY_LIGHTNING_BOLT_THUNDER"
      
  # Custom announcements
  custom:
    chest-refill: "&e&l📦 &eChests refilled! New loot available!"
    half-players: "&e&l📊 &eHalf the players remain! &c%remaining% &eleft!"
    final-three: "&c&l🔥 &cFinal 3 players! The end is near!"
    final-two: "&4&l⚔ &4Final 2 players! Who will be victorious?"

# 🎨 Visual Indicators
visual-indicators:
  # Boss bar
  boss-bar:
    enabled: true
    
    # Phase-specific boss bars
    grace-period:
      title: "&e&lGrace Period: &c%time%"
      color: "YELLOW"
      style: "SOLID"
      
    normal-phase:
      title: "&a&lSkyWars: &e%remaining% &aplayers remaining"
      color: "GREEN"
      style: "SEGMENTED_10"
      
    border-shrink:
      title: "&c&lBorder Shrinking: &e%time%"
      color: "RED"
      style: "SOLID"
      
    deathmatch:
      title: "&4&lDeathmatch: &c%time%"
      color: "RED"
      style: "SEGMENTED_6"
      
    sudden-death:
      title: "&4&lSUDDEN DEATH: &c%time%"
      color: "RED"
      style: "SOLID"
      
  # Action bar
  action-bar:
    enabled: true
    update-interval: 1  # Update every second
    
    # Phase-specific action bars
    grace-period: "&e🛡 Grace Period: &c%time%s &7| &e👥 Players: &a%players%"
    normal-phase: "&a⚔ Game Time: &e%time% &7| &c💀 Players: &e%remaining% &7| &c🗡 Kills: &e%kills%"
    border-shrink: "&c⚠ Border: &e%time%s &7| &c💀 Players: &e%remaining%"
    deathmatch: "&4💀 Deathmatch: &c%time%s &7| &4⚔ FIGHT!"
    sudden-death: "&4💀 SUDDEN DEATH: &c%time%s"
    
  # Scoreboard
  scoreboard:
    enabled: true
    title: "&b&lSkyWars"
    update-interval: 2  # Update every 2 seconds
    
    # Scoreboard lines (dynamic based on phase)
    lines:
      - ""
      - "&7Map: &e%arena%"
      - "&7Mode: &e%mode%"
      - ""
      - "&7Phase: &e%phase%"
      - "&7Time: &e%time%"
      - ""
      - "&7Players: &a%remaining%"
      - "&7Kills: &c%kills%"
      - ""
      - "&7Rank: &6#%rank%"
      - ""
      - "&ewww.yourserver.com"

# 🔧 Performance Optimization
performance:
  # Timer optimization
  timers:
    precision: 20         # Tick precision (20 = 1 second)
    async-processing: true
    batch-updates: true
    
  # Memory management
  memory:
    cleanup-interval: 300  # 5 minutes
    cache-size: 1000
    
  # Database optimization
  database:
    batch-timing-updates: true
    update-interval: 30    # 30 seconds
    
# 🎮 Emergency Controls
emergency:
  # Force end conditions
  force-end:
    enabled: true
    max-duration: 1800     # 30 minutes absolute maximum
    server-restart: true   # End games on server restart
    low-tps: true         # End games if TPS too low
    
  # Admin controls
  admin-controls:
    force-phase: true      # Allow admins to force phase changes
    extend-time: true      # Allow time extensions
    emergency-stop: true   # Emergency game termination
    
# 📊 Timing Analytics
analytics:
  enabled: true
  
  # Tracked metrics
  metrics:
    average-game-duration: true
    phase-durations: true
    player-elimination-times: true
    server-performance: true
    
  # Data collection
  collection:
    detailed-logs: true
    performance-impact: true
    player-feedback: true
