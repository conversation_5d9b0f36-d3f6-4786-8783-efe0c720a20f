#!/bin/bash
# Server Fallback System

# Function to check server health
check_server_health() {
    if docker ps | grep -q minecraft-server-docker-mc-1; then
        # Check if server is responsive
        if docker exec minecraft-server-docker-mc-1 rcon-cli "list" >/dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# Function to start server in safe mode
start_safe_mode() {
    echo "Starting server in safe mode..."
    
    # Create safe mode configuration
    cat > safe-mode-server.properties << 'SAFEEOF'
# Safe Mode Server Configuration
online-mode=false
difficulty=peaceful
gamemode=adventure
max-players=10
view-distance=6
spawn-protection=16
enable-command-block=false
SAFEEOF

    # Backup current server.properties
    if [[ -f "files/server.properties" ]]; then
        cp files/server.properties files/server.properties.backup
    fi
    
    # Use safe mode configuration
    cp safe-mode-server.properties files/server.properties
    
    # Restart server
    docker-compose restart mc
    
    echo "Server started in safe mode"
}

# Function to handle server fallback
handle_server_fallback() {
    if ! check_server_health; then
        echo "Server health check failed, attempting recovery..."
        
        # Try normal restart first
        docker-compose restart mc
        
        # Wait for restart
        sleep 30
        
        if ! check_server_health; then
            echo "Normal restart failed, starting in safe mode..."
            start_safe_mode
        else
            echo "Server recovered successfully"
        fi
    fi
}

