#!/bin/bash
# Master Fallback Controller
# Coordinates all fallback systems

# Source all fallback systems
source scripts/database-fallback.sh 2>/dev/null || true
source scripts/plugin-fallback.sh 2>/dev/null || true
source scripts/server-fallback.sh 2>/dev/null || true
source scripts/fallback-commands.sh 2>/dev/null || true

# Function to activate all fallback systems
activate_fallback_mode() {
    echo "🛡️ Activating SkyWars Fallback Mode..."
    echo "====================================="
    
    # Check and handle server issues
    handle_server_fallback
    
    # Check and handle database issues
    handle_database_fallback
    
    # Check and handle plugin issues
    handle_plugin_fallbacks
    
    # Set fallback mode flag
    export SKYWARS_FALLBACK_MODE=true
    echo "SKYWARS_FALLBACK_MODE=true" > .env.fallback
    
    echo "✅ Fallback mode activated"
    echo ""
    echo "📋 Fallback Features Active:"
    echo "   • Basic SkyWars commands"
    echo "   • File-based data storage"
    echo "   • Simplified game mechanics"
    echo "   • Safe mode server operation"
    echo ""
    echo "⚠️  Some advanced features may be unavailable"
}

# Function to deactivate fallback mode
deactivate_fallback_mode() {
    echo "🔄 Deactivating SkyWars Fallback Mode..."
    echo "======================================="
    
    # Remove fallback mode flag
    unset SKYWARS_FALLBACK_MODE
    rm -f .env.fallback
    
    # Restore normal configurations
    if [[ -f "files/server.properties.backup" ]]; then
        cp files/server.properties.backup files/server.properties
        docker-compose restart mc
    fi
    
    echo "✅ Fallback mode deactivated"
    echo "🔄 System restored to normal operation"
}

# Function to check if fallback mode is needed
check_fallback_needed() {
    local issues=0
    
    # Check server health
    if ! check_server_health 2>/dev/null; then
        echo "⚠️  Server health issues detected"
        ((issues++))
    fi
    
    # Check database health
    if ! check_database_health 2>/dev/null; then
        echo "⚠️  Database health issues detected"
        ((issues++))
    fi
    
    # Check critical plugins
    local critical_plugins=("AuthMe" "Essentials")
    for plugin in "${critical_plugins[@]}"; do
        if ! check_plugin_loaded "$plugin" 2>/dev/null; then
            echo "⚠️  Critical plugin missing: $plugin"
            ((issues++))
        fi
    done
    
    if [[ $issues -gt 0 ]]; then
        echo "🛡️ $issues issue(s) detected - fallback mode recommended"
        return 0
    else
        echo "✅ All systems healthy - fallback mode not needed"
        return 1
    fi
}

# Main fallback handler
main() {
    local action="${1:-check}"
    
    case "$action" in
        "activate")
            activate_fallback_mode
            ;;
        "deactivate")
            deactivate_fallback_mode
            ;;
        "check")
            check_fallback_needed
            ;;
        "auto")
            if check_fallback_needed; then
                activate_fallback_mode
            fi
            ;;
        *)
            echo "Usage: $0 {activate|deactivate|check|auto}"
            echo ""
            echo "Commands:"
            echo "  activate   - Force activate fallback mode"
            echo "  deactivate - Deactivate fallback mode"
            echo "  check      - Check if fallback mode is needed"
            echo "  auto       - Automatically activate if needed"
            ;;
    esac
}

# Export main function
export -f activate_fallback_mode
export -f deactivate_fallback_mode
export -f check_fallback_needed

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
