#!/bin/bash
# Command Handler System
# Handles SkyWars command execution with proper validation

# Source required modules
source scripts/skywars-unified-commands.sh 2>/dev/null || true
source scripts/command-registry.sh 2>/dev/null || true

# Function to validate command permissions
validate_command_permission() {
    local player="$1"
    local command="$2"
    local required_permission="$3"
    
    # Check if player has required permission
    local has_permission=$(docker exec minecraft-server-docker-mc-1 rcon-cli "lp user $player permission check $required_permission" 2>/dev/null | grep -o "true\|false" || echo "false")
    
    if [[ "$has_permission" == "true" ]]; then
        return 0
    else
        return 1
    fi
}

# Function to handle command execution
handle_command_execution() {
    local player="$1"
    local full_command="$2"
    
    # Parse command
    local command_parts=($full_command)
    local base_command="${command_parts[0]}"
    local sub_command="${command_parts[1]:-}"
    local args="${command_parts[@]:2}"
    
    # Determine full command key
    local command_key="$base_command"
    if [[ -n "$sub_command" ]]; then
        command_key="$base_command $sub_command"
    fi
    
    # Get required permission
    local required_permission=""
    if [[ -n "${SKYWARS_COMMANDS[$command_key]:-}" ]]; then
        required_permission="${SKYWARS_COMMANDS[$command_key]}"
    else
        # Default permission for unknown commands
        required_permission="skywars.player.use"
    fi
    
    # Validate permission
    if ! validate_command_permission "$player" "$command_key" "$required_permission"; then
        docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"You don't have permission to use this command!\",\"color\":\"red\"}"
        return 1
    fi
    
    # Execute command
    case "$base_command" in
        "sw"|"skywars")
            if command -v handle_skywars_command &> /dev/null; then
                handle_skywars_command "$player" "$sub_command" $args
            else
                docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"SkyWars command system not available\",\"color\":\"red\"}"
            fi
            ;;
        *)
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Unknown command: $base_command\",\"color\":\"red\"}"
            return 1
            ;;
    esac
}

# Function to register command aliases
register_command_aliases() {
    echo "🔗 Registering command aliases..."
    
    # Create alias configuration for plugins that support it
    cat > config/command-aliases.yml << 'ALIASES'
# SkyWars Command Aliases
aliases:
  # Short aliases
  swa: "sw admin"
  swj: "sw join"
  swl: "sw leave"
  sws: "sw stats"
  swt: "sw top"
  swsp: "sw spectate"
  swh: "sw help"
  
  # Alternative names
  skywars: "sw"
  skywar: "sw"
  sky: "sw"
  
  # Admin aliases
  swadmin: "sw admin"
  swarena: "sw arena"
  swgame: "sw game"
  swreload: "sw reload"
  
  # Quick actions
  join: "sw join"
  leave: "sw leave"
  stats: "sw stats"
  top: "sw top"
ALIASES

    echo "✅ Command aliases registered"
}

# Function to create command help system
create_command_help() {
    echo "📚 Creating command help system..."
    
    cat > scripts/command-help.sh << 'HELPSCRIPT'
#!/bin/bash
# Command Help System

show_help() {
    local player="$1"
    local command="${2:-general}"
    
    case "$command" in
        "general"|"help")
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"=== SkyWars Commands ===\",\"color\":\"gold\"}"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw join [mode] - Join a SkyWars game\",\"color\":\"yellow\"}"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw leave - Leave current game/queue\",\"color\":\"yellow\"}"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw stats [player] - View statistics\",\"color\":\"yellow\"}"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw top [type] - View leaderboards\",\"color\":\"yellow\"}"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw spectate - Spectate games\",\"color\":\"yellow\"}"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw help [command] - Show help\",\"color\":\"yellow\"}"
            ;;
        "join")
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"=== Join Command Help ===\",\"color\":\"gold\"}"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw join solo - Join solo game (1v1v1...)\",\"color\":\"yellow\"}"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw join doubles - Join doubles game (2v2v2...)\",\"color\":\"yellow\"}"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw join ranked - Join ranked game\",\"color\":\"yellow\"}"
            ;;
        "admin")
            if validate_command_permission "$player" "sw admin" "skywars.admin.use"; then
                docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"=== Admin Commands ===\",\"color\":\"gold\"}"
                docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw admin - Open admin panel\",\"color\":\"yellow\"}"
                docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw arena [action] - Arena management\",\"color\":\"yellow\"}"
                docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw game [action] - Game management\",\"color\":\"yellow\"}"
                docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw reload - Reload configuration\",\"color\":\"yellow\"}"
            else
                docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"You don't have permission to view admin commands\",\"color\":\"red\"}"
            fi
            ;;
        *)
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Unknown help topic: $command\",\"color\":\"red\"}"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Use /sw help for general help\",\"color\":\"gray\"}"
            ;;
    esac
}

export -f show_help
HELPSCRIPT

    chmod +x scripts/command-help.sh
    echo "✅ Command help system created"
}

# Function to test command system
test_command_system() {
    log_cmd "Testing command system..."
    
    # Test permission registration
    if source scripts/command-registry.sh && register_permissions; then
        log_success "Permission registration test passed"
    else
        log_warning "Permission registration test failed"
    fi
    
    # Test command handler
    if [[ -f "scripts/command-handler.sh" ]]; then
        log_success "Command handler system available"
    else
        log_warning "Command handler system not found"
    fi
    
    # Test help system
    if [[ -f "scripts/command-help.sh" ]]; then
        log_success "Command help system available"
    else
        log_warning "Command help system not found"
    fi
}

# Export functions
export -f validate_command_permission
export -f handle_command_execution
export -f register_command_aliases
export -f create_command_help
