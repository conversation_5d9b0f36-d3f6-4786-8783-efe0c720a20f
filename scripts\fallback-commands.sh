#!/bin/bash
# Fallback Command System
# Basic commands when main system fails

handle_fallback_command() {
    local player="$1"
    local command="$2"
    
    case "$command" in
        "join")
            # Basic join functionality
            docker exec minecraft-server-docker-mc-1 rcon-cli "tp $player 0 70 0"
            docker exec minecraft-server-docker-mc-1 rcon-cli "gamemode survival $player"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Joined basic SkyWars mode\",\"color\":\"green\"}"
            ;;
        "leave")
            # Basic leave functionality
            docker exec minecraft-server-docker-mc-1 rcon-cli "tp $player 0 70 0"
            docker exec minecraft-server-docker-mc-1 rcon-cli "gamemode adventure $player"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Left SkyWars\",\"color\":\"yellow\"}"
            ;;
        "stats")
            # Basic stats display
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Stats temporarily unavailable\",\"color\":\"red\"}"
            ;;
        *)
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Available: join, leave, stats\",\"color\":\"gray\"}"
            ;;
    esac
}

# Export function
export -f handle_fallback_command
