#!/bin/bash
# AuthMe Integration for SkyWars

# Function to check if player is authenticated
check_player_auth() {
    local player="$1"
    
    # Check if player is logged in via AuthMe
    local auth_status=$(docker exec minecraft-server-docker-mc-1 rcon-cli "authme islogged $player" 2>/dev/null || echo "false")
    
    if echo "$auth_status" | grep -q "true\|logged"; then
        echo "true"
    else
        echo "false"
    fi
}

# Function to require authentication before SkyWars
require_auth_for_skywars() {
    local player="$1"
    
    if [[ "$(check_player_auth "$player")" == "false" ]]; then
        docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Please login first with /login <password>\",\"color\":\"red\"}"
        return 1
    fi
    
    return 0
}

# Export functions for use in other scripts
export -f check_player_auth
export -f require_auth_for_skywars
