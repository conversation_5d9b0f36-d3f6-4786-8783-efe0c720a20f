#!/bin/bash
# Command Registry System
# Manages SkyWars command registration and permissions

# Command definitions
declare -A SKYWARS_COMMANDS=(
    # Player commands
    ["sw"]="skywars.player.use"
    ["skywars"]="skywars.player.use"
    ["sw join"]="skywars.player.join"
    ["sw leave"]="skywars.player.leave"
    ["sw stats"]="skywars.player.stats"
    ["sw top"]="skywars.player.leaderboard"
    ["sw spectate"]="skywars.player.spectate"
    ["sw help"]="skywars.player.help"
    
    # Admin commands
    ["sw admin"]="skywars.admin.use"
    ["sw arena"]="skywars.admin.arena"
    ["sw game"]="skywars.admin.game"
    ["sw reload"]="skywars.admin.reload"
    ["sw kick"]="skywars.admin.kick"
    ["sw ban"]="skywars.admin.ban"
    ["sw unban"]="skywars.admin.unban"
    ["sw reset"]="skywars.admin.reset"
    
    # Moderator commands
    ["sw mod"]="skywars.mod.use"
    ["sw forcestart"]="skywars.mod.forcestart"
    ["sw forceend"]="skywars.mod.forceend"
    ["sw tp"]="skywars.mod.teleport"
)

# Permission groups
declare -A PERMISSION_GROUPS=(
    ["player"]="skywars.player.*"
    ["vip"]="skywars.player.* skywars.vip.*"
    ["moderator"]="skywars.player.* skywars.mod.*"
    ["admin"]="skywars.player.* skywars.mod.* skywars.admin.*"
    ["owner"]="skywars.*"
)

# Function to register SkyWars permissions
register_permissions() {
    echo "🔐 Registering SkyWars permissions..."
    
    # Register individual permissions
    for permission in "${SKYWARS_COMMANDS[@]}"; do
        docker exec minecraft-server-docker-mc-1 rcon-cli "lp creategroup skywars_temp" 2>/dev/null || true
        docker exec minecraft-server-docker-mc-1 rcon-cli "lp group skywars_temp permission set $permission true" 2>/dev/null || true
        docker exec minecraft-server-docker-mc-1 rcon-cli "lp deletegroup skywars_temp" 2>/dev/null || true
    done
    
    # Register permission groups
    for group in "${!PERMISSION_GROUPS[@]}"; do
        local permissions="${PERMISSION_GROUPS[$group]}"
        
        # Create group if it doesn't exist
        docker exec minecraft-server-docker-mc-1 rcon-cli "lp creategroup skywars_$group" 2>/dev/null || true
        
        # Add permissions to group
        for perm in $permissions; do
            docker exec minecraft-server-docker-mc-1 rcon-cli "lp group skywars_$group permission set $perm true" 2>/dev/null || true
        done
        
        echo "✅ Registered permission group: skywars_$group"
    done
}

# Function to register default permissions
register_default_permissions() {
    echo "🔐 Setting up default permissions..."
    
    # Give basic permissions to default group
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group default permission set skywars.player.use true" 2>/dev/null || true
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group default permission set skywars.player.join true" 2>/dev/null || true
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group default permission set skywars.player.leave true" 2>/dev/null || true
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group default permission set skywars.player.stats true" 2>/dev/null || true
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group default permission set skywars.player.help true" 2>/dev/null || true
    
    echo "✅ Default permissions configured"
}

# Function to check command registration
check_command_registration() {
    echo "🔍 Checking command registration..."
    
    local registered_commands=0
    local total_commands=${#SKYWARS_COMMANDS[@]}
    
    # Check if commands are available (this would depend on the actual plugin)
    for command in "${!SKYWARS_COMMANDS[@]}"; do
        # For now, just count as registered since we can't easily check plugin commands
        ((registered_commands++))
    done
    
    echo "Commands registered: $registered_commands/$total_commands"
    
    if [[ $registered_commands -eq $total_commands ]]; then
        echo "✅ All commands are registered"
        return 0
    else
        echo "⚠️  Some commands may not be registered"
        return 1
    fi
}

# Export functions
export -f register_permissions
export -f register_default_permissions
export -f check_command_registration
