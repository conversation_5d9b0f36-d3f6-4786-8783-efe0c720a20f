# 📊 Professional SkyWars Statistics System
# Comprehensive player tracking and leaderboard management
# Version: 2.0 - Professional Edition

# 📈 Statistics Configuration
statistics:
  # Global settings
  global:
    enabled: true
    real-time-updates: true
    detailed-tracking: true
    performance-mode: false
    
  # Database configuration
  database:
    enabled: true
    type: "mysql"
    host: "db"
    port: 3306
    database: "minecraft-abusaker"
    username: "hamza"
    password: "Hh@#2021"
    table-prefix: "skywars_"
    
    # Connection settings
    connection:
      pool-size: 10
      timeout: 30
      auto-reconnect: true
      
  # Tracked statistics
  tracked-stats:
    # Basic stats
    basic:
      wins: true
      losses: true
      games-played: true
      time-played: true
      
    # Combat stats
    combat:
      kills: true
      deaths: true
      damage-dealt: true
      damage-taken: true
      kill-streak-best: true
      arrows-shot: true
      arrows-hit: true
      
    # Gameplay stats
    gameplay:
      blocks-placed: true
      blocks-broken: true
      chests-opened: true
      survival-time: true
      placement-average: true
      
    # Economy stats
    economy:
      coins-earned: true
      coins-spent: true
      experience-gained: true
      level: true
      
    # Advanced stats
    advanced:
      win-rate: true
      kdr: true
      accuracy: true
      average-placement: true
      games-per-day: true

# 🏆 Leaderboard System
leaderboards:
  # Global settings
  global:
    enabled: true
    update-interval: 300  # 5 minutes
    display-count: 10
    cache-duration: 600   # 10 minutes
    
  # Leaderboard types
  types:
    wins:
      enabled: true
      name: "&6&lMost Wins"
      description: "&7Players with the most victories"
      stat: "wins"
      order: "DESC"
      icon: "GOLDEN_SWORD"
      
    kills:
      enabled: true
      name: "&c&lMost Kills"
      description: "&7Players with the most eliminations"
      stat: "kills"
      order: "DESC"
      icon: "DIAMOND_SWORD"
      
    kdr:
      enabled: true
      name: "&e&lBest K/D Ratio"
      description: "&7Players with the best kill/death ratio"
      stat: "kdr"
      order: "DESC"
      icon: "BOW"
      min-games: 10  # Minimum games to appear
      
    win-rate:
      enabled: true
      name: "&a&lBest Win Rate"
      description: "&7Players with the highest win percentage"
      stat: "win_rate"
      order: "DESC"
      icon: "EMERALD"
      min-games: 20
      
    time-played:
      enabled: true
      name: "&b&lMost Active"
      description: "&7Players with the most playtime"
      stat: "time_played"
      order: "DESC"
      icon: "CLOCK"
      
    level:
      enabled: true
      name: "&d&lHighest Level"
      description: "&7Players with the highest level"
      stat: "level"
      order: "DESC"
      icon: "EXPERIENCE_BOTTLE"
      
  # Leaderboard periods
  periods:
    all-time:
      enabled: true
      name: "&6All Time"
      description: "&7Statistics since server start"
      
    monthly:
      enabled: true
      name: "&bMonthly"
      description: "&7Statistics for current month"
      reset-day: 1  # Reset on 1st of month
      
    weekly:
      enabled: true
      name: "&aWeekly"
      description: "&7Statistics for current week"
      reset-day: 1  # Reset on Monday (1=Monday, 7=Sunday)
      
    daily:
      enabled: true
      name: "&eDaily"
      description: "&7Statistics for today"
      reset-hour: 0  # Reset at midnight

# 🎯 Achievement System
achievements:
  # Global settings
  global:
    enabled: true
    notifications: true
    rewards: true
    progress-tracking: true
    
  # Achievement categories
  categories:
    combat:
      name: "&c&lCombat Achievements"
      description: "&7Achievements related to fighting"
      icon: "DIAMOND_SWORD"
      
    survival:
      name: "&a&lSurvival Achievements"
      description: "&7Achievements related to surviving"
      icon: "GOLDEN_APPLE"
      
    exploration:
      name: "&e&lExploration Achievements"
      description: "&7Achievements related to exploring"
      icon: "COMPASS"
      
    mastery:
      name: "&5&lMastery Achievements"
      description: "&7Achievements for true masters"
      icon: "NETHER_STAR"
      
  # Achievement definitions
  definitions:
    # Combat achievements
    first-kill:
      name: "&cFirst Blood"
      description: "&7Get your first kill"
      category: "combat"
      requirement: {type: "kills", value: 1}
      reward: {xp: 50, coins: 100}
      rarity: "COMMON"
      
    kill-streak-5:
      name: "&cUnstoppable"
      description: "&7Get a 5 kill streak in one game"
      category: "combat"
      requirement: {type: "kill_streak", value: 5}
      reward: {xp: 200, coins: 500}
      rarity: "RARE"
      
    hundred-kills:
      name: "&cCenturion"
      description: "&7Reach 100 total kills"
      category: "combat"
      requirement: {type: "kills", value: 100}
      reward: {xp: 500, coins: 1000}
      rarity: "EPIC"
      
    # Survival achievements
    first-win:
      name: "&aVictorious"
      description: "&7Win your first game"
      category: "survival"
      requirement: {type: "wins", value: 1}
      reward: {xp: 100, coins: 200}
      rarity: "COMMON"
      
    ten-wins:
      name: "&aChampion"
      description: "&7Win 10 games"
      category: "survival"
      requirement: {type: "wins", value: 10}
      reward: {xp: 300, coins: 600}
      rarity: "UNCOMMON"
      
    survivor:
      name: "&aSurvivor"
      description: "&7Survive for 10 minutes in a game"
      category: "survival"
      requirement: {type: "survival_time", value: 600}
      reward: {xp: 150, coins: 300}
      rarity: "UNCOMMON"
      
    # Exploration achievements
    chest-hunter:
      name: "&eChest Hunter"
      description: "&7Open 100 chests"
      category: "exploration"
      requirement: {type: "chests_opened", value: 100}
      reward: {xp: 200, coins: 400}
      rarity: "UNCOMMON"
      
    builder:
      name: "&eBuilder"
      description: "&7Place 1000 blocks"
      category: "exploration"
      requirement: {type: "blocks_placed", value: 1000}
      reward: {xp: 250, coins: 500}
      rarity: "RARE"
      
    # Mastery achievements
    master:
      name: "&5SkyWars Master"
      description: "&7Reach level 50"
      category: "mastery"
      requirement: {type: "level", value: 50}
      reward: {xp: 1000, coins: 2000}
      rarity: "LEGENDARY"
      
    perfectionist:
      name: "&5Perfectionist"
      description: "&7Achieve 90% win rate with 50+ games"
      category: "mastery"
      requirement: {type: "win_rate", value: 90, min_games: 50}
      reward: {xp: 2000, coins: 5000}
      rarity: "LEGENDARY"

# 📊 Statistics Display
display:
  # Personal statistics
  personal:
    format:
      header: "&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"
      title: "&6&l🏆 SkyWars Statistics - %player%"
      separator: "&7&l━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
      footer: "&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"
      
    # Stat lines
    lines:
      - "&a&l🏆 &aWins: &e%wins% &7| &c&l💀 &cLosses: &e%losses% &7| &b&l📈 &bWin Rate: &6%win_rate%%"
      - "&c&l⚔ &cKills: &e%kills% &7| &7&l💀 &7Deaths: &e%deaths% &7| &e&l📊 &eK/D Ratio: &6%kdr%"
      - "&7&l🎮 &7Games: &e%games_played% &7| &d&l⏰ &dTime: &e%time_played%"
      - "&6&l⭐ &6Level: &e%level% &7| &a&l💰 &aCoins: &e%coins% &7| &b&l✨ &bXP: &e%experience%"
      - "&c&l🔥 &cBest Streak: &e%kill_streak_best% &7| &e&l🎯 &eAccuracy: &6%accuracy%%"
      
  # Leaderboard display
  leaderboard:
    format:
      header: "&6&l🏆 SkyWars Leaderboard - %type% (%period%)"
      entry: "&e#%position% &7- &6%player% &7- &e%value%"
      footer: "&7&oUse &e/sw stats &7&oto view your statistics!"
      
    # Entry formatting
    entry-format:
      top-3: "&%color%#%position% &7- &%color%&l%player% &7- &%color%&l%value%"
      regular: "&e#%position% &7- &6%player% &7- &e%value%"
      
    # Colors for top positions
    colors:
      1: "6"  # Gold
      2: "7"  # Silver
      3: "c"  # Bronze

# 🔄 Data Management
data-management:
  # Update intervals
  updates:
    real-time: true
    batch-interval: 30    # seconds
    leaderboard-interval: 300  # 5 minutes
    
  # Data retention
  retention:
    match-history: 90     # days
    detailed-stats: 365   # days
    leaderboard-history: 30  # days
    
  # Cleanup
  cleanup:
    auto-cleanup: true
    cleanup-interval: 86400  # 24 hours
    archive-old-data: true
    
# 🎨 User Interface
ui:
  # Scoreboard
  scoreboard:
    enabled: true
    title: "&b&lSkyWars Stats"
    update-interval: 5    # seconds
    
    # Scoreboard lines
    lines:
      - ""
      - "&7Level: &e%level%"
      - "&7XP: &a%experience%"
      - "&7Coins: &6%coins%"
      - ""
      - "&7Wins: &a%wins%"
      - "&7Kills: &c%kills%"
      - "&7K/D: &e%kdr%"
      - ""
      - "&7Rank: &6#%rank%"
      - ""
      - "&ewww.yourserver.com"
      
  # Action bar
  action-bar:
    enabled: true
    format: "&7Level &e%level% &7| &aWins: &e%wins% &7| &cKills: &e%kills% &7| &6Rank: &e#%rank%"
    
# 🔧 Performance Settings
performance:
  # Caching
  caching:
    enabled: true
    cache-size: 1000
    cache-duration: 600   # 10 minutes
    
  # Database optimization
  database:
    batch-updates: true
    connection-pooling: true
    query-optimization: true
    
  # Memory management
  memory:
    cleanup-interval: 300  # 5 minutes
    max-memory-usage: 100  # MB
