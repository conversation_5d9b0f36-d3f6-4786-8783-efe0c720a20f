#!/bin/bash
# Configuration Recovery Script

echo "🔧 Configuration Recovery Procedure"
echo "==================================="

# Check for critical configuration files
critical_configs=(
    "plugins/SkyWars/config.yml"
    "plugins/SkyWars/arenas.yml"
)

for config in "${critical_configs[@]}"; do
    if [[ ! -f "$config" ]]; then
        echo "❌ Missing critical config: $config"
        
        # Restore from backup if available
        if [[ -f "$config.backup" ]]; then
            echo "🔄 Restoring from backup..."
            cp "$config.backup" "$config"
            echo "✅ Restored $config"
        else
            echo "⚠️  No backup found, creating minimal config..."
            # Create minimal working configuration
            mkdir -p "$(dirname "$config")"
            echo "# Minimal SkyWars Configuration" > "$config"
            echo "enabled: true" >> "$config"
            echo "✅ Created minimal $config"
        fi
    else
        echo "✅ $config exists"
    fi
done
