#!/bin/bash
# Unified SkyWars Command System
# Integrates with all existing plugins

# Source integration scripts
source scripts/authme-integration.sh 2>/dev/null || true
source scripts/essentials-integration.sh 2>/dev/null || true
source scripts/multiverse-integration.sh 2>/dev/null || true

# Main SkyWars command handler
handle_skywars_command() {
    local player="$1"
    local command="$2"
    local args="${@:3}"
    
    # Check authentication first (if AuthMe is available)
    if command -v require_auth_for_skywars &> /dev/null; then
        if ! require_auth_for_skywars "$player"; then
            return 1
        fi
    fi
    
    case "$command" in
        "join")
            handle_join_command "$player" "$args"
            ;;
        "leave")
            handle_leave_command "$player"
            ;;
        "stats")
            handle_stats_command "$player" "$args"
            ;;
        "spectate")
            handle_spectate_command "$player" "$args"
            ;;
        "help")
            handle_help_command "$player"
            ;;
        *)
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Unknown SkyWars command. Use /sw help\",\"color\":\"red\"}"
            ;;
    esac
}

# Join command handler
handle_join_command() {
    local player="$1"
    local mode="${2:-solo}"
    
    # Check if player is in lobby world
    local current_world=$(docker exec minecraft-server-docker-mc-1 rcon-cli "data get entity $player Dimension" 2>/dev/null | grep -o '[^:]*$' || echo "unknown")
    
    if [[ "$current_world" != "lobby" ]]; then
        # Teleport to lobby first
        if command -v teleport_to_lobby &> /dev/null; then
            teleport_to_lobby "$player"
        fi
    fi
    
    # Create SkyWars world if needed
    if command -v create_skywars_world &> /dev/null; then
        create_skywars_world "modern_skywar"
        configure_skywars_world "modern_skywar"
    fi
    
    # Join queue (basic implementation)
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Joining $mode SkyWars queue...\",\"color\":\"green\"}"
    
    # Teleport to SkyWars world
    if command -v teleport_to_world &> /dev/null; then
        teleport_to_world "$player" "modern_skywar" 0 70 0
    fi
    
    # Give starting items
    docker exec minecraft-server-docker-mc-1 rcon-cli "clear $player"
    docker exec minecraft-server-docker-mc-1 rcon-cli "give $player stone_sword 1"
    docker exec minecraft-server-docker-mc-1 rcon-cli "give $player bread 5"
    docker exec minecraft-server-docker-mc-1 rcon-cli "give $player oak_planks 16"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Welcome to SkyWars! Good luck!\",\"color\":\"gold\"}"
}

# Leave command handler
handle_leave_command() {
    local player="$1"
    
    # Teleport back to lobby
    if command -v teleport_to_lobby &> /dev/null; then
        teleport_to_lobby "$player"
    else
        docker exec minecraft-server-docker-mc-1 rcon-cli "tp $player 0 70 0"
    fi
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Left SkyWars and returned to lobby\",\"color\":\"yellow\"}"
}

# Stats command handler
handle_stats_command() {
    local player="$1"
    local target="${2:-$player}"
    
    # Get player balance if Essentials is available
    local balance="Unknown"
    if command -v check_player_balance &> /dev/null; then
        balance=$(check_player_balance "$target")
    fi
    
    # Display basic stats (would be enhanced with database integration)
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"=== SkyWars Stats for $target ===\",\"color\":\"gold\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Wins: 0 | Losses: 0 | Kills: 0\",\"color\":\"aqua\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Balance: $balance coins\",\"color\":\"green\"}"
}

# Spectate command handler
handle_spectate_command() {
    local player="$1"
    
    # Teleport to SkyWars world in spectator mode
    if command -v teleport_to_world &> /dev/null; then
        teleport_to_world "$player" "modern_skywar" 0 80 0
    fi
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "gamemode spectator $player"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"You are now spectating SkyWars!\",\"color\":\"gray\"}"
}

# Help command handler
handle_help_command() {
    local player="$1"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"=== SkyWars Commands ===\",\"color\":\"gold\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw join [solo|doubles] - Join a game\",\"color\":\"yellow\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw leave - Leave current game\",\"color\":\"yellow\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw stats [player] - View statistics\",\"color\":\"yellow\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw spectate - Spectate games\",\"color\":\"yellow\"}"
}

# Command aliases
alias sw='handle_skywars_command'
alias skywars='handle_skywars_command'

# Export main function
export -f handle_skywars_command
