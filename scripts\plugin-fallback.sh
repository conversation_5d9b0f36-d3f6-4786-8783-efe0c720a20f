#!/bin/bash
# Plugin Fallback System

# Function to check if plugin is loaded
check_plugin_loaded() {
    local plugin_name="$1"
    local plugins=$(docker exec minecraft-server-docker-mc-1 rcon-cli "plugins" 2>/dev/null || echo "")
    
    if echo "$plugins" | grep -q "$plugin_name"; then
        return 0
    else
        return 1
    fi
}

# Function to handle AuthMe fallback
handle_authme_fallback() {
    if ! check_plugin_loaded "AuthMe"; then
        echo "AuthMe not available, using basic authentication"
        
        # Create basic auth function
        basic_auth_check() {
            local player="$1"
            # Always return true for fallback mode
            return 0
        }
        
        export -f basic_auth_check
    fi
}

# Function to handle Essentials fallback
handle_essentials_fallback() {
    if ! check_plugin_loaded "Essentials"; then
        echo "Essentials not available, using basic commands"
        
        # Create basic teleport function
        basic_teleport() {
            local player="$1"
            local x="${2:-0}"
            local y="${3:-70}"
            local z="${4:-0}"
            
            docker exec minecraft-server-docker-mc-1 rcon-cli "tp $player $x $y $z"
        }
        
        export -f basic_teleport
    fi
}

# Function to handle Multiverse fallback
handle_multiverse_fallback() {
    if ! check_plugin_loaded "Multiverse-Core"; then
        echo "Multiverse not available, using basic world management"
        
        # Create basic world teleport function
        basic_world_tp() {
            local player="$1"
            local world="$2"
            
            # Use basic tp command
            docker exec minecraft-server-docker-mc-1 rcon-cli "tp $player 0 70 0"
        }
        
        export -f basic_world_tp
    fi
}

# Main plugin fallback handler
handle_plugin_fallbacks() {
    echo "Checking plugin availability and setting up fallbacks..."
    
    handle_authme_fallback
    handle_essentials_fallback
    handle_multiverse_fallback
    
    echo "Plugin fallback systems ready"
}

# Export main function
export -f handle_plugin_fallbacks
