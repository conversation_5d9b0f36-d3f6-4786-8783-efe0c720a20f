#!/bin/bash
# Command Validation System
# Validates command syntax and parameters

# Function to validate player name
validate_player_name() {
    local player_name="$1"
    
    # Check if player name is valid (alphanumeric, underscore, 3-16 characters)
    if [[ "$player_name" =~ ^[a-zA-Z0-9_]{3,16}$ ]]; then
        return 0
    else
        return 1
    fi
}

# Function to validate game mode
validate_game_mode() {
    local mode="$1"
    local valid_modes=("solo" "doubles" "squads" "ranked")
    
    for valid_mode in "${valid_modes[@]}"; do
        if [[ "$mode" == "$valid_mode" ]]; then
            return 0
        fi
    done
    
    return 1
}

# Function to validate arena name
validate_arena_name() {
    local arena="$1"
    
    # Check if arena name is valid (alphanumeric, underscore, dash, 3-32 characters)
    if [[ "$arena" =~ ^[a-zA-Z0-9_-]{3,32}$ ]]; then
        return 0
    else
        return 1
    fi
}

# Function to validate command arguments
validate_command_args() {
    local command="$1"
    shift
    local args=("$@")
    
    case "$command" in
        "join")
            if [[ ${#args[@]} -eq 0 ]]; then
                # Default to solo mode
                return 0
            elif [[ ${#args[@]} -eq 1 ]]; then
                validate_game_mode "${args[0]}"
            else
                return 1
            fi
            ;;
        "stats")
            if [[ ${#args[@]} -eq 0 ]]; then
                # Show own stats
                return 0
            elif [[ ${#args[@]} -eq 1 ]]; then
                validate_player_name "${args[0]}"
            else
                return 1
            fi
            ;;
        "kick"|"ban"|"unban")
            if [[ ${#args[@]} -ge 1 ]]; then
                validate_player_name "${args[0]}"
            else
                return 1
            fi
            ;;
        "arena")
            if [[ ${#args[@]} -ge 2 ]]; then
                local action="${args[0]}"
                local arena="${args[1]}"
                validate_arena_name "$arena"
            else
                return 1
            fi
            ;;
        *)
            # Unknown command, assume valid
            return 0
            ;;
    esac
}

# Function to sanitize command input
sanitize_command_input() {
    local input="$1"
    
    # Remove potentially dangerous characters
    local sanitized=$(echo "$input" | sed 's/[;&|`$(){}[\]\\]//g')
    
    # Limit length
    if [[ ${#sanitized} -gt 100 ]]; then
        sanitized="${sanitized:0:100}"
    fi
    
    echo "$sanitized"
}

# Function to validate and sanitize full command
validate_full_command() {
    local player="$1"
    local raw_command="$2"
    
    # Sanitize input
    local sanitized_command=$(sanitize_command_input "$raw_command")
    
    # Parse command
    local command_parts=($sanitized_command)
    local base_command="${command_parts[0]}"
    local sub_command="${command_parts[1]:-}"
    local args=("${command_parts[@]:2}")
    
    # Validate base command
    if [[ "$base_command" != "sw" && "$base_command" != "skywars" ]]; then
        docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Invalid command: $base_command\",\"color\":\"red\"}"
        return 1
    fi
    
    # Validate sub command and arguments
    if ! validate_command_args "$sub_command" "${args[@]}"; then
        docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Invalid command syntax. Use /sw help for usage.\",\"color\":\"red\"}"
        return 1
    fi
    
    echo "$sanitized_command"
    return 0
}

# Export functions
export -f validate_player_name
export -f validate_game_mode
export -f validate_arena_name
export -f validate_command_args
export -f sanitize_command_input
export -f validate_full_command
